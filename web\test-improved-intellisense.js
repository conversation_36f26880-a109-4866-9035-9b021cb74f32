// 测试改进后的智能提示功能
const testData = {
  functions: [
    {
      value: "UUID",
      label: "随机ID",
      script: "Utils.UUID()",
      remark: "生成随机的UUID字符串",
      category: "Common",
      categoryDisplayName: "常用函数",
      parameters: [],
      returnType: "string",
      outputType: "string",
      examples: [
        {
          title: "基本用法",
          code: "Utils.UUID()",
          result: "\"abc123def456\"",
          description: "生成一个随机的UUID字符串"
        },
        {
          title: "在变量中使用",
          code: "var id = Utils.UUID(); console.log(id);",
          result: "\"abc123def456\"",
          description: "将生成的UUID保存到变量中"
        }
      ]
    },
    {
      value: "DATE_TO_STRING",
      label: "时间转文本",
      script: "Utils.DATE_TO_STRING(time, format)",
      remark: "将时间转换为指定格式的字符串",
      category: "Common",
      categoryDisplayName: "常用函数",
      parameters: [
        {
          name: "time",
          type: "DateTime",
          required: true,
          description: "要转换的时间"
        },
        {
          name: "format",
          type: "string",
          required: false,
          defaultValue: "yyyy-MM-dd HH:mm:ss",
          description: "时间格式"
        }
      ],
      returnType: "string",
      outputType: "string",
      examples: [
        {
          title: "默认格式",
          code: "Utils.DATE_TO_STRING(Utils.NOW())",
          result: "\"2024-01-15 14:30:25\"",
          description: "使用默认格式转换时间"
        },
        {
          title: "日期格式",
          code: "Utils.DATE_TO_STRING(Utils.NOW(), \"yyyy-MM-dd\")",
          result: "\"2024-01-15\"",
          description: "只显示日期部分"
        },
        {
          title: "时间格式",
          code: "Utils.DATE_TO_STRING(Utils.NOW(), \"HH:mm:ss\")",
          result: "\"14:30:25\"",
          description: "只显示时间部分"
        },
        {
          title: "中文格式",
          code: "Utils.DATE_TO_STRING(Utils.NOW(), \"yyyy年MM月dd日\")",
          result: "\"2024年01月15日\"",
          description: "使用中文日期格式"
        }
      ]
    },
    {
      value: "JSON_PARSE",
      label: "JSON解析",
      script: "Utils.JSON_PARSE(jsonString)",
      remark: "解析JSON字符串为对象",
      category: "Common",
      categoryDisplayName: "常用函数",
      parameters: [
        {
          name: "jsonString",
          type: "string",
          required: true,
          description: "JSON字符串"
        }
      ],
      returnType: "object",
      outputType: "object",
      examples: [
        {
          title: "解析对象",
          code: "Utils.JSON_PARSE('{\"name\":\"张三\",\"age\":25}')",
          result: "{name: \"张三\", age: 25}",
          description: "解析JSON字符串为对象"
        },
        {
          title: "解析数组",
          code: "Utils.JSON_PARSE('[1,2,3,4,5]')",
          result: "[1, 2, 3, 4, 5]",
          description: "解析JSON数组"
        },
        {
          title: "访问属性",
          code: "Utils.JSON_PARSE('{\"user\":{\"name\":\"李四\"}}').user.name",
          result: "\"李四\"",
          description: "解析后访问嵌套属性"
        }
      ]
    }
  ]
};

console.log('=== 测试改进后的智能提示数据 ===');
console.log('函数数量:', testData.functions.length);

testData.functions.forEach((func, index) => {
  console.log(`\n=== 函数 ${index + 1}: ${func.value} ===`);
  console.log('英文名称:', func.value);
  console.log('中文名称:', func.label);
  console.log('描述:', func.remark);
  console.log('参数数量:', func.parameters?.length || 0);
  
  if (func.parameters && func.parameters.length > 0) {
    console.log('参数详情:');
    func.parameters.forEach((param, i) => {
      const required = param.required ? '必需' : '可选';
      const defaultValue = param.defaultValue !== undefined ? ` (默认: ${param.defaultValue})` : '';
      console.log(`  ${i + 1}. ${param.name} (${param.type}, ${required}): ${param.description}${defaultValue}`);
    });
  }
  
  console.log('返回类型:', func.returnType);
  console.log('示例数量:', func.examples?.length || 0);
  
  if (func.examples && func.examples.length > 0) {
    console.log('示例预览:');
    func.examples.slice(0, 2).forEach((example, i) => {
      console.log(`  ${i + 1}. ${example.title}: ${example.code}`);
    });
  }
});

// 测试生成TypeScript类型定义
function generateTestTypeDefinition() {
  const utilsMethods = testData.functions.map((func) => {
    // 生成参数类型定义
    const params = func.parameters?.map((param) => {
      const paramType = param.type === 'DateTime' ? 'Date' : param.type.toLowerCase();
      const optional = !param.required ? '?' : '';
      return `${param.name}${optional}: ${paramType}`;
    }).join(', ') || '';

    // 生成返回类型
    const returnType = func.returnType === 'DateTime' ? 'Date' : func.returnType.toLowerCase();

    return `  /**
   * ${func.remark}
   * @example ${func.examples?.[0]?.code || func.script}
   */
  ${func.value}(${params}): ${returnType};`;
  }).join('\n\n');

  const typeDefinition = `declare const Utils: {
${utilsMethods}
};`;

  console.log('\n=== 生成的TypeScript类型定义 ===');
  console.log(typeDefinition);
}

generateTestTypeDefinition();

console.log('\n=== 测试完成 ===');
console.log('改进内容:');
console.log('1. 智能提示显示英文函数名 (value) 而不是中文名 (label)');
console.log('2. 添加完整的TypeScript类型定义，包括参数类型和返回值类型');
console.log('3. 增强的文档显示，包含参数说明、返回值说明和使用示例');
console.log('4. 支持JSDoc格式的函数注释');
