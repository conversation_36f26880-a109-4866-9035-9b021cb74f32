using GCP.Common;
using GCP.DataAccess;
using GCP.Functions.Common.VisualFunction;
using GCP.Functions.Common.Attributes;
using System.Diagnostics;
using System.Reflection;
using System.Text.Json;

namespace GCP.Functions.Common.Services
{
    /// <summary>
    /// 可视化函数服务
    /// </summary>
    [Function("visualFunction", "可视化函数服务")]
    internal class VisualFunctionService : BaseService
    {
        /// <summary>
        /// 执行可视化函数
        /// </summary>
        /// <param name="request">执行请求</param>
        /// <returns>执行结果</returns>
        [Function("execute", "执行可视化函数")]
        public async Task<VisualFunctionExecuteResponse> ExecuteAsync(VisualFunctionExecuteRequest request)
        {
            try
            {


                var stopwatch = Stopwatch.StartNew();
                var engine = new VisualFunctionEngine(this.Context.db);
                var response = new VisualFunctionExecuteResponse();

                // 生成C#表达式
                response.GeneratedExpression = engine.GenerateCSharpExpression(request.Steps);

                // 如果只需要生成表达式，直接返回
                if (request.GenerateExpressionOnly)
                {
                    response.ExecutionTimeMs = stopwatch.ElapsedMilliseconds;
                    return response;
                }

                // 执行函数
                if (request.UseCompiledExpression)
                {
                    // 使用编译表达式执行（高性能模式）
                    response.Result = await engine.ExecuteAsync(request.Steps, request.InputData, true);

                    // 生成简化的步骤结果详情
                    response.StepResults = request.Steps.OrderBy(s => s.Order).Select(step => new VisualFunctionStepResult
                    {
                        StepId = step.Id,
                        StepName = step.DisplayName,
                        Result = $"步骤 {step.Order + 1} 执行完成",
                        ExecutionTimeMs = response.ExecutionTimeMs / request.Steps.Count, // 平均分配时间
                        Success = true
                    }).ToList();
                }
                else
                {
                    // 使用解释执行（调试模式，获取详细步骤信息）
                    var context = new VisualFunctionContext
                    {
                        InputData = request.InputData ?? new Dictionary<string, object>(),
                        Variables = new Dictionary<string, object>(),
                        StepResults = new Dictionary<string, object>()
                    };

                    response.Result = await engine.ExecuteInterpretedWithDetailsAsync(request.Steps, context);
                    response.StepResults = context.StepExecutionDetails;
                }

                response.ExecutionTimeMs = stopwatch.ElapsedMilliseconds;

                return response;
            }
            catch (Exception ex)
            {
                return new VisualFunctionExecuteResponse
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    ExecutionTimeMs = 0,
                    StepResults = request.Steps.Select(step => new VisualFunctionStepResult
                    {
                        StepId = step.Id,
                        StepName = step.DisplayName,
                        Success = false,
                        ErrorMessage = ex.Message
                    }).ToList()
                };
            }
        }

        /// <summary>
        /// 测试单个函数步骤
        /// </summary>
        /// <param name="step">函数步骤</param>
        /// <param name="inputData">输入数据</param>
        /// <returns>测试结果</returns>
        [Function("testStep", "测试单个函数步骤")]
        public async Task<object> TestStepAsync(VisualFunctionStep step, Dictionary<string, object>? inputData = null)
        {
            try
            {
                var stopwatch = Stopwatch.StartNew();
                var engine = new VisualFunctionEngine(this.Context.db);
                
                var steps = new List<VisualFunctionStep> { step };
                var result = await engine.ExecuteAsync(steps, inputData ?? new Dictionary<string, object>());
                
                return new
                {
                    Success = true,
                    Result = result,
                    ExecutionTimeMs = stopwatch.ElapsedMilliseconds
                };
            }
            catch (Exception ex)
            {
                return new
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <summary>
        /// 获取可用的内置函数列表
        /// </summary>
        /// <returns>函数列表</returns>
        [Function("getBuiltinFunctions", "获取内置函数列表")]
        public List<object> GetBuiltinFunctions()
        {
            try
            {
                var functions = GetBuiltinFunctionList();
                return functions;
            }
            catch (Exception ex)
            {
                throw new CustomException($"获取内置函数列表失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 验证可视化函数配置
        /// </summary>
        /// <param name="steps">函数步骤列表</param>
        /// <returns>验证结果</returns>
        [Function("validate", "验证可视化函数配置")]
        public object ValidateConfiguration(List<VisualFunctionStep> steps)
        {
            try
            {
                var validationResult = ValidateSteps(steps);
                return validationResult;
            }
            catch (Exception ex)
            {
                throw new CustomException($"验证配置失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 保存可视化函数配置（已移除，不再需要单独的配置表）
        /// </summary>
        /// <param name="configuration">函数配置</param>
        /// <returns>保存结果</returns>
        [Function("saveConfiguration", "保存可视化函数配置")]
        public async Task<object> SaveConfigurationAsync(VisualFunctionConfiguration configuration)
        {
            // 可视化函数配置不再需要单独保存，直接在使用时执行
            return new { Success = true, Message = "可视化函数配置无需保存，直接执行即可" };
        }

        /// <summary>
        /// 获取保存的可视化函数配置列表（已移除）
        /// </summary>
        /// <returns>配置列表</returns>
        [Function("getConfigurations", "获取可视化函数配置列表")]
        public async Task<List<VisualFunctionConfiguration>> GetConfigurationsAsync()
        {
            // 不再需要保存配置，返回空列表
            return new List<VisualFunctionConfiguration>();
        }

        /// <summary>
        /// 删除可视化函数配置（已移除）
        /// </summary>
        /// <param name="id">配置ID</param>
        /// <returns>删除结果</returns>
        [Function("deleteConfiguration", "删除可视化函数配置")]
        public async Task<object> DeleteConfigurationAsync(string id)
        {
            // 不再需要删除配置
            return new { Success = true, Message = "无需删除配置" };
        }

        #region 私有方法

        /// <summary>
        /// 获取内置函数列表
        /// </summary>
        private List<object> GetBuiltinFunctionList()
        {
            var functions = new List<object>();
            var utilsType = typeof(ScriptExtensions.JavascriptUtils);
            var methods = utilsType.GetMethods(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance)
                .Where(m => !m.IsSpecialName && m.DeclaringType == utilsType);

            foreach (var method in methods)
            {
                // 获取UtilsFunction属性
                var utilsFunctionAttr = method.GetCustomAttribute<UtilsFunctionAttribute>();

                // 如果没有属性，跳过该方法
                if (utilsFunctionAttr == null) continue;

                var parameters = method.GetParameters().Select(p =>
                {
                    // 获取参数描述属性
                    var paramDescAttr = p.GetCustomAttribute<ParameterDescriptionAttribute>();
                    var description = paramDescAttr?.Description ?? GetParameterDescription(p.Name);

                    return new
                    {
                        Name = p.Name,
                        Type = GetParameterTypeName(p.ParameterType),
                        Required = !p.HasDefaultValue,
                        DefaultValue = p.HasDefaultValue ? p.DefaultValue : null,
                        Description = description
                    };
                }).ToList();

                // 获取使用示例
                var examples = GetFunctionExamples(method, utilsFunctionAttr);

                functions.Add(new
                {
                    Value = method.Name,
                    Name = method.Name,
                    Label = utilsFunctionAttr.DisplayName,
                    DisplayName = utilsFunctionAttr.DisplayName,
                    Description = utilsFunctionAttr.Description,
                    Remark = utilsFunctionAttr.Description,
                    Script = GenerateFunctionScript(method.Name, method.GetParameters()),
                    Category = utilsFunctionAttr.Category,
                    CategoryDisplayName = utilsFunctionAttr.CategoryDisplayName,
                    Parameters = parameters,
                    ReturnType = GetParameterTypeName(method.ReturnType),
                    OutputType = utilsFunctionAttr.OutputType ?? GetParameterTypeName(method.ReturnType),
                    Examples = examples
                });
            }

            return functions.OrderBy(f => ((dynamic)f).Category).ThenBy(f => ((dynamic)f).Label).ToList();
        }



        /// <summary>
        /// 获取参数类型名称
        /// </summary>
        private string GetParameterTypeName(Type type)
        {
            if (type == typeof(string)) return "string";
            if (type == typeof(int) || type == typeof(int?)) return "int";
            if (type == typeof(double) || type == typeof(double?)) return "decimal";
            if (type == typeof(bool) || type == typeof(bool?)) return "bool";
            if (type == typeof(DateTime) || type == typeof(DateTime?)) return "DateTime";
            if (type.IsArray || (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(List<>))) return "array";
            if (type == typeof(object) || type.Name.Contains("Dictionary")) return "object";
            return "any";
        }

        /// <summary>
        /// 获取参数描述
        /// </summary>
        private string GetParameterDescription(string paramName)
        {
            return paramName switch
            {
                "input" => "Input string",
                "array" => "Input array",
                "dict" => "Dictionary object",
                "date" => "Date object",
                "date1" => "First date",
                "date2" => "Second date",
                "value" => "Input value",
                "value1" => "First value",
                "value2" => "Second value",
                "condition" => "Boolean condition",
                "trueValue" => "Value when condition is true",
                "falseValue" => "Value when condition is false",
                "key" => "Dictionary key",
                "time" => "Time object",
                "jsonString" => "JSON string",
                "dateString" => "Date string",
                "timestamp" => "Unix timestamp",
                "separator" => "Separator character",
                "format" => "Format string",
                "startIndex" => "Start position",
                "length" => "Length",
                "oldValue" => "Value to replace",
                "newValue" => "New value",
                "defaultValue" => "Default value",
                "digits" => "Decimal places",
                "unit" => "Time unit",
                "groupFields" => "Group fields array",
                "fields" => "Other fields array",
                "childField" => "Child field name",
                "dict1" => "First dictionary",
                "dict2" => "Second dictionary",
                "operator_" => "Math operator",
                "sqlString" => "SQL query string",
                "args" => "Query parameters",
                _ => $"{paramName} parameter"
            };
        }

        /// <summary>
        /// 生成函数脚本
        /// </summary>
        private string GenerateFunctionScript(string functionName, System.Reflection.ParameterInfo[] parameters)
        {
            var paramList = parameters.Select(p =>
            {
                if (p.HasDefaultValue)
                {
                    var defaultValue = p.DefaultValue?.ToString() ?? "null";
                    if (p.ParameterType == typeof(string) && p.DefaultValue != null)
                        defaultValue = $"\"{defaultValue}\"";
                    return p.Name;
                }
                return p.Name;
            });

            return $"Utils.{functionName}({string.Join(", ", paramList)})";
        }

        /// <summary>
        /// 获取函数使用示例
        /// </summary>
        private List<object> GetFunctionExamples(MethodInfo method, UtilsFunctionAttribute utilsFunctionAttr)
        {
            var examples = new List<object>();

            // 1. 从 FunctionExample 属性获取示例
            var exampleAttrs = method.GetCustomAttributes<FunctionExampleAttribute>();
            foreach (var exampleAttr in exampleAttrs)
            {
                examples.Add(new
                {
                    Title = exampleAttr.Title,
                    Code = exampleAttr.Code,
                    Result = exampleAttr.Result,
                    Description = exampleAttr.Description
                });
            }

            // 2. 从 UtilsFunction 属性的 Examples 字段获取示例
            if (!string.IsNullOrEmpty(utilsFunctionAttr.Examples))
            {
                try
                {
                    var jsonExamples = JsonSerializer.Deserialize<List<object>>(utilsFunctionAttr.Examples);
                    if (jsonExamples != null)
                    {
                        examples.AddRange(jsonExamples);
                    }
                }
                catch (JsonException ex)
                {
                    // 如果JSON解析失败，记录错误但不影响其他功能
                    Console.WriteLine($"解析函数 {method.Name} 的示例JSON失败: {ex.Message}");
                }
            }

            // 3. 如果没有定义示例，生成默认示例
            if (examples.Count == 0)
            {
                examples.AddRange(GenerateDefaultExamples(method));
            }

            return examples;
        }

        /// <summary>
        /// 生成默认使用示例
        /// </summary>
        private List<object> GenerateDefaultExamples(MethodInfo method)
        {
            var examples = new List<object>();
            var parameters = method.GetParameters();

            // 基本用法示例
            examples.Add(new
            {
                Title = "基本用法",
                Code = GenerateFunctionScript(method.Name, parameters),
                Result = (string)null,
                Description = "基本调用方式"
            });

            // 如果有参数，生成详细示例
            if (parameters.Length > 0)
            {
                var exampleParams = parameters.Select(p => GenerateExampleValue(p)).ToArray();
                var detailedCode = $"Utils.{method.Name}({string.Join(", ", exampleParams)})";

                examples.Add(new
                {
                    Title = "详细示例",
                    Code = detailedCode,
                    Result = (string)null,
                    Description = "带具体参数的调用示例"
                });
            }

            return examples;
        }

        /// <summary>
        /// 为参数生成示例值
        /// </summary>
        private string GenerateExampleValue(System.Reflection.ParameterInfo parameter)
        {
            var type = parameter.ParameterType;
            var name = parameter.Name.ToLower();

            // 根据参数名称生成特定示例
            if (name.Contains("format"))
                return "\"yyyy-MM-dd HH:mm:ss\"";
            if (name.Contains("separator"))
                return "\",\"";
            if (name.Contains("unit"))
                return "\"days\"";

            // 根据类型生成示例
            if (type == typeof(string))
                return $"\"{parameter.Name}示例\"";
            if (type == typeof(int) || type == typeof(int?))
                return "123";
            if (type == typeof(double) || type == typeof(double?) || type == typeof(decimal) || type == typeof(decimal?))
                return "123.45";
            if (type == typeof(bool) || type == typeof(bool?))
                return "true";
            if (type == typeof(DateTime) || type == typeof(DateTime?))
                return "new Date()";
            if (type.IsArray || (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(List<>)))
                return "[1, 2, 3]";
            if (type == typeof(object) || type.Name.Contains("Dictionary"))
                return "{ key: \"value\" }";

            // 如果有默认值，使用默认值
            if (parameter.HasDefaultValue)
            {
                var defaultValue = parameter.DefaultValue;
                if (defaultValue == null)
                    return "null";
                if (type == typeof(string))
                    return $"\"{defaultValue}\"";
                return defaultValue.ToString();
            }

            return parameter.Name;
        }



        /// <summary>
        /// 验证函数步骤
        /// </summary>
        private object ValidateSteps(List<VisualFunctionStep> steps)
        {
            var errors = new List<string>();
            var warnings = new List<string>();

            if (steps == null || steps.Count == 0)
            {
                errors.Add("至少需要一个函数步骤");
                return new { Success = false, Errors = errors, Warnings = warnings };
            }

            foreach (var step in steps)
            {
                // 验证函数名称
                if (string.IsNullOrEmpty(step.FunctionName))
                {
                    errors.Add($"步骤 {step.Order + 1} 缺少函数名称");
                }

                // 验证参数
                foreach (var param in step.Parameters)
                {
                    if (param.Required && (param.Value == null || string.IsNullOrEmpty(param.Value.ToString())))
                    {
                        errors.Add($"步骤 {step.Order + 1} 的必需参数 '{param.Name}' 不能为空");
                    }
                }

                // 检查输出变量名冲突
                if (!string.IsNullOrEmpty(step.OutputVariable))
                {
                    var duplicates = steps.Where(s => s.Id != step.Id && s.OutputVariable == step.OutputVariable).ToList();
                    if (duplicates.Any())
                    {
                        warnings.Add($"输出变量名 '{step.OutputVariable}' 在多个步骤中重复使用");
                    }
                }
            }

            return new
            {
                Success = errors.Count == 0,
                Errors = errors,
                Warnings = warnings
            };
        }

        #endregion
    }
}
