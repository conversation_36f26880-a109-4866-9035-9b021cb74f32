// 测试参数数量提示功能
console.log('=== 测试Utils函数参数数量提示 ===\n');

// 模拟不同参数数量的函数
const testFunctions = [
  {
    value: "UUID",
    label: "随机ID",
    script: "Utils.UUID()",
    remark: "生成随机的UUID字符串",
    parameters: [], // 无参数
    returnType: "string",
    examples: [
      {
        title: "基本用法",
        code: "Utils.UUID()",
        result: "\"abc123def456\"",
        description: "生成一个随机的UUID字符串"
      }
    ]
  },
  {
    value: "NOW",
    label: "当前时间",
    script: "Utils.NOW()",
    remark: "获取当前日期和时间",
    parameters: [], // 无参数
    returnType: "DateTime",
    examples: [
      {
        title: "基本用法",
        code: "Utils.NOW()",
        result: "2024-01-15 14:30:25",
        description: "获取当前的日期和时间"
      }
    ]
  },
  {
    value: "DATE_TO_STRING",
    label: "时间转文本",
    script: "Utils.DATE_TO_STRING(time, format)",
    remark: "将时间转换为指定格式的字符串",
    parameters: [ // 2个参数
      {
        name: "time",
        type: "DateTime",
        required: true,
        description: "要转换的时间"
      },
      {
        name: "format",
        type: "string",
        required: false,
        defaultValue: "yyyy-MM-dd HH:mm:ss",
        description: "时间格式"
      }
    ],
    returnType: "string",
    examples: [
      {
        title: "默认格式",
        code: "Utils.DATE_TO_STRING(Utils.NOW())",
        result: "\"2024-01-15 14:30:25\"",
        description: "使用默认格式转换时间"
      }
    ]
  },
  {
    value: "ARRAY_CONTAINS",
    label: "数组包含",
    script: "Utils.ARRAY_CONTAINS(array, value)",
    remark: "检查数组是否包含指定元素",
    parameters: [ // 2个参数
      {
        name: "array",
        type: "object",
        required: true,
        description: "输入数组"
      },
      {
        name: "value",
        type: "object",
        required: true,
        description: "要检查的值"
      }
    ],
    returnType: "boolean",
    examples: [
      {
        title: "基本用法",
        code: "Utils.ARRAY_CONTAINS([1,2,3], 2)",
        result: "true",
        description: "检查数组是否包含指定值"
      }
    ]
  },
  {
    value: "STRING_REPLACE",
    label: "字符串替换",
    script: "Utils.STRING_REPLACE(text, oldValue, newValue, ignoreCase)",
    remark: "替换字符串中的指定内容",
    parameters: [ // 4个参数
      {
        name: "text",
        type: "string",
        required: true,
        description: "原始字符串"
      },
      {
        name: "oldValue",
        type: "string",
        required: true,
        description: "要替换的内容"
      },
      {
        name: "newValue",
        type: "string",
        required: true,
        description: "新内容"
      },
      {
        name: "ignoreCase",
        type: "boolean",
        required: false,
        defaultValue: false,
        description: "是否忽略大小写"
      }
    ],
    returnType: "string",
    examples: [
      {
        title: "基本替换",
        code: "Utils.STRING_REPLACE(\"Hello World\", \"World\", \"Vue\")",
        result: "\"Hello Vue\"",
        description: "替换字符串中的指定内容"
      }
    ]
  }
];

// 模拟改进后的智能提示生成
function generateIntelliSenseSuggestion(func) {
  // 生成带参数数量的label
  const paramCount = func.parameters?.length || 0;
  const labelWithParamCount = paramCount > 0 ? `${func.value}(${paramCount})` : `${func.value}()`;
  
  // 生成函数签名
  const params = func.parameters?.map(param => {
    const optional = !param.required ? '?' : '';
    return `${param.name}${optional}: ${param.type}`;
  }).join(', ') || '';
  
  const returnType = func.returnType || 'any';
  const paramCountText = paramCount > 0 ? ` (${paramCount}个参数)` : ' (无参数)';
  const signature = `${func.value}(${params}): ${returnType}${paramCountText}`;
  
  return {
    label: labelWithParamCount,
    detail: signature,
    paramCount: paramCount
  };
}

console.log('智能提示效果预览:');
console.log('=====================================');

testFunctions.forEach((func, index) => {
  const suggestion = generateIntelliSenseSuggestion(func);
  
  console.log(`${index + 1}. ${func.label} (${func.value})`);
  console.log(`   智能提示显示: ${suggestion.label}`);
  console.log(`   函数签名: ${suggestion.detail}`);
  console.log(`   参数数量: ${suggestion.paramCount}个`);
  
  if (func.parameters && func.parameters.length > 0) {
    console.log('   参数详情:');
    func.parameters.forEach((param, i) => {
      const required = param.required ? '必需' : '可选';
      const defaultValue = param.defaultValue !== undefined ? ` (默认: ${param.defaultValue})` : '';
      console.log(`     ${i + 1}. ${param.name} (${param.type}, ${required}): ${param.description}${defaultValue}`);
    });
  }
  console.log('');
});

console.log('改进效果对比:');
console.log('=====================================');
console.log('改进前:');
console.log('  UUID          -> label: "UUID"');
console.log('  DATE_TO_STRING -> label: "DATE_TO_STRING"');
console.log('  STRING_REPLACE -> label: "STRING_REPLACE"');
console.log('');
console.log('改进后:');
console.log('  UUID          -> label: "UUID()" (显示无参数)');
console.log('  DATE_TO_STRING -> label: "DATE_TO_STRING(2)" (显示2个参数)');
console.log('  STRING_REPLACE -> label: "STRING_REPLACE(4)" (显示4个参数)');
console.log('');

console.log('✅ 改进优势:');
console.log('1. 一眼就能看出函数有几个参数');
console.log('2. 便于快速选择合适的函数');
console.log('3. 减少查看文档的次数');
console.log('4. 提升开发效率');
console.log('');
console.log('🎯 测试完成！参数数量提示功能已实现。');
