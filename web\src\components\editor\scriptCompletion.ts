import * as monaco from 'monaco-editor';

// 变量和函数数据接口
interface VariableData {
  id: string;
  key: string;
  path?: string;
  pathDescription?: string;
  description?: string;
  type: string;
  children?: VariableData[];
}

interface FunctionData {
  value: string;
  label: string;
  script: string;
  remark: string;
  category?: string;
  categoryDisplayName?: string;
  parameters?: Array<{
    name: string;
    type: string;
    required: boolean;
    defaultValue?: any;
    description?: string;
    isParams?: boolean;
  }>;
  returnType?: string;
  outputType?: string;
  examples?: Array<{
    title: string;
    code: string;
    result?: string;
    description: string;
  }>;
}

// 全局变量存储
let currentVariables: VariableData[] = [];
let localVariables: VariableData[] = [];
let globalVariables: VariableData[] = [];
let functions: FunctionData[] = [];

// 生成详细的类型定义，支持原生方法
const generateDetailedTypeDefinitions = (variables: VariableData[], interfaceName: string): string => {
  const generateInterface = (vars: VariableData[], name: string): string => {
    const properties = vars
      .map((variable) => {
        let typeStr = '';

        // 根据类型生成TypeScript类型
        switch (variable.type.toLowerCase()) {
          case 'string':
            typeStr = 'string';
            break;
          case 'number':
          case 'int':
          case 'integer':
          case 'float':
          case 'double':
            typeStr = 'number';
            break;
          case 'boolean':
          case 'bool':
            typeStr = 'boolean';
            break;
          case 'array':
            typeStr = 'any[]';
            break;
          case 'object':
            if (variable.children && variable.children.length > 0) {
              // 生成嵌套接口
              const nestedInterfaceName = `${name}_${variable.key}`;
              const nestedInterface = generateInterface(variable.children, nestedInterfaceName);
              typeStr = nestedInterfaceName;
              return nestedInterface + `\n  ${variable.key}: ${typeStr};`;
            } else {
              typeStr = 'any';
            }
            break;
          default:
            if (variable.children && variable.children.length > 0) {
              // 如果有子属性，生成嵌套接口
              const nestedInterfaceName = `${name}_${variable.key}`;
              const nestedInterface = generateInterface(variable.children, nestedInterfaceName);
              typeStr = nestedInterfaceName;
              return nestedInterface + `\n  ${variable.key}: ${typeStr};`;
            } else {
              typeStr = 'any';
            }
        }

        return `  ${variable.key}: ${typeStr};`;
      })
      .join('\n');

    return `interface ${name} {\n${properties}\n}`;
  };

  return generateInterface(variables, interfaceName);
};

// 生成Utils类型定义
const generateUtilsTypeDefinition = (): string => {
  if (functions.length === 0) {
    return 'declare const Utils: any;';
  }

  const utilsMethods = functions
    .map((func) => {
      // 生成参数类型定义
      const params =
        func.parameters
          ?.map((param) => {
            const paramType = getTypeScriptType(param.type);
            const optional = !param.required ? '?' : '';
            return `${param.name}${optional}: ${paramType}`;
          })
          .join(', ') || '';

      // 生成返回类型
      const returnType = func.returnType ? getTypeScriptType(func.returnType) : 'any';

      // 生成JSDoc注释
      const jsdoc = generateJSDocComment(func);

      return `${jsdoc}
  ${func.value}(${params}): ${returnType};`;
    })
    .join('\n\n');

  return `declare const Utils: {
${utilsMethods}
};`;
};

// 生成JSDoc注释
const generateJSDocComment = (func: FunctionData): string => {
  const lines = ['  /**'];

  // 添加函数描述
  if (func.remark) {
    lines.push(`   * ${func.remark}`);
  }

  // 添加参数说明
  if (func.parameters && func.parameters.length > 0) {
    lines.push('   *');
    func.parameters.forEach((param) => {
      const description = param.description || '';
      lines.push(`   * @param ${param.name} ${description}`);
    });
  }

  // 添加返回值说明
  if (func.returnType) {
    lines.push('   *');
    lines.push(`   * @returns ${func.outputType || func.returnType}`);
  }

  // 添加示例
  if (func.examples && func.examples.length > 0) {
    lines.push('   *');
    lines.push('   * @example');
    func.examples.slice(0, 2).forEach((example) => {
      lines.push(`   * // ${example.title}`);
      lines.push(`   * ${example.code}`);
      if (example.result) {
        lines.push(`   * // => ${example.result}`);
      }
      lines.push('   *');
    });
  }

  lines.push('   */');
  return lines.join('\n');
};

// 更新Monaco Editor的类型定义
const updateMonacoTypeDefinitions = () => {
  try {
    // 生成详细的类型定义，支持原生方法
    const localTypeDef =
      localVariables.length > 0 ? generateDetailedTypeDefinitions(localVariables, 'LocalVariables') : '';
    const globalTypeDef =
      globalVariables.length > 0 ? generateDetailedTypeDefinitions(globalVariables, 'GlobalVariables') : '';

    // 生成_data对象的类型定义
    let dataTypeDef = '';
    if (localVariables.length > 0 || globalVariables.length > 0) {
      const dataProperties = [];

      // 处理局部变量
      if (localVariables.length > 0) {
        dataProperties.push(
          ...localVariables.map((v) => {
            if (v.children && v.children.length > 0) {
              return `  ${v.key}: LocalVariables_${v.key};`;
            } else {
              return `  ${v.key}: ${getTypeScriptType(v.type)};`;
            }
          }),
        );
      }

      // 处理全局变量
      if (globalVariables.length > 0) {
        dataProperties.push(
          ...globalVariables.map((v) => {
            if (v.children && v.children.length > 0) {
              return `  ${v.key}: GlobalVariables_${v.key};`;
            } else {
              return `  ${v.key}: ${getTypeScriptType(v.type)};`;
            }
          }),
        );
      }

      dataTypeDef = `interface DataObject {\n${dataProperties.join('\n')}\n}`;
    }

    // 生成临时变量的类型定义，支持原生方法
    const currentTypeDef =
      currentVariables.length > 0 ? generateDetailedTypeDefinitions(currentVariables, 'CurrentVariables') : '';

    // 生成Utils类型定义
    const utilsTypeDef = generateUtilsTypeDefinition();

    // 组合所有类型定义
    const allTypeDefs = [
      localTypeDef,
      globalTypeDef,
      currentTypeDef,
      dataTypeDef,
      'declare const _data: DataObject;',
      utilsTypeDef,
      // 为当前变量提供详细的类型定义，支持原生方法
      ...currentVariables.map((v) => {
        if (v.children && v.children.length > 0) {
          return `declare const ${v.key}: CurrentVariables_${v.key};`;
        } else {
          return `declare const ${v.key}: ${getTypeScriptType(v.type)};`;
        }
      }),
    ]
      .filter((def) => def)
      .join('\n\n');

    // 清除之前的类型定义
    try {
      monaco.languages.typescript.typescriptDefaults.setExtraLibs([]);
      monaco.languages.typescript.javascriptDefaults.setExtraLibs([]);
    } catch (error) {
      console.warn('清除旧类型定义失败:', error);
    }

    // 添加到Monaco Editor
    if (allTypeDefs) {
      monaco.languages.typescript.typescriptDefaults.addExtraLib(allTypeDefs, 'ts:custom-variables.d.ts');
      monaco.languages.typescript.javascriptDefaults.addExtraLib(allTypeDefs, 'ts:custom-variables.d.ts');
      console.log('已更新Monaco Editor类型定义:', allTypeDefs);
    }
  } catch (error) {
    console.error('更新Monaco Editor类型定义失败:', error);
  }
};

// 获取TypeScript类型字符串
const getTypeScriptType = (type: string): string => {
  switch (type.toLowerCase()) {
    case 'string':
      return 'string';
    case 'number':
    case 'int':
    case 'integer':
    case 'float':
    case 'double':
      return 'number';
    case 'boolean':
    case 'bool':
      return 'boolean';
    case 'array':
      return 'any[]';
    default:
      return 'any';
  }
};

// 处理ROOT节点，展开其子节点（递归处理）
const expandRootNodes = (variables: VariableData[]): VariableData[] => {
  const result: VariableData[] = [];

  variables.forEach((variable) => {
    if (variable.key === 'ROOT' && variable.children && variable.children.length > 0) {
      // 如果是ROOT节点，递归展开其子节点
      const expandedChildren = expandRootNodes(variable.children);
      result.push(...expandedChildren);
    } else {
      // 非ROOT节点，递归处理其子节点
      const processedVariable = { ...variable };
      if (processedVariable.children && processedVariable.children.length > 0) {
        processedVariable.children = expandRootNodes(processedVariable.children);
      }
      result.push(processedVariable);
    }
  });

  return result;
};

// 更新智能提示数据的函数
export const updateIntelliSenseData = (data: {
  currentVariables?: VariableData[];
  localVariables?: VariableData[];
  globalVariables?: VariableData[];
  functions?: FunctionData[];
}) => {
  if (data.currentVariables) {
    // 处理ROOT节点展开
    currentVariables = expandRootNodes(data.currentVariables);
    console.log('更新当前变量:', currentVariables);
  }
  if (data.localVariables) {
    // 处理ROOT节点展开
    localVariables = expandRootNodes(data.localVariables);
    console.log('更新局部变量:', localVariables);
  }
  if (data.globalVariables) {
    // 处理ROOT节点展开
    globalVariables = expandRootNodes(data.globalVariables);
    console.log('更新全局变量:', globalVariables);
  }
  if (data.functions) {
    functions = data.functions;
    console.log('更新函数列表:', functions);
    console.log(
      '函数详细信息:',
      functions.map((f) => ({
        value: f.value,
        label: f.label,
        parameters: f.parameters,
        returnType: f.returnType,
        examples: f.examples?.length || 0,
      })),
    );
  }

  // 更新Monaco Editor的类型定义
  updateMonacoTypeDefinitions();
};

// 扁平化变量列表，生成所有可能的路径
const flattenVariables = (
  variables: VariableData[],
  prefix: string = '',
): Array<{ path: string; variable: VariableData }> => {
  const result: Array<{ path: string; variable: VariableData }> = [];

  variables.forEach((variable) => {
    const currentPath = prefix ? `${prefix}.${variable.key}` : variable.key;
    result.push({ path: currentPath, variable });

    if (variable.children && variable.children.length > 0) {
      result.push(...flattenVariables(variable.children, currentPath));
    }
  });

  return result;
};

// 去重智能提示建议
const deduplicateSuggestions = (suggestions: any[]): any[] => {
  const seen = new Set<string>();
  return suggestions.filter((suggestion) => {
    const key = suggestion.label;
    if (seen.has(key)) {
      return false;
    }
    seen.add(key);
    return true;
  });
};

// 获取第一级变量建议（只显示第一级，不展平）
const getFirstLevelSuggestions = (variables: VariableData[]): any[] => {
  return variables.map((variable) => ({
    label: variable.key,
    kind:
      variable.children && variable.children.length > 0
        ? monaco.languages.CompletionItemKind.Module
        : monaco.languages.CompletionItemKind.Variable,
    insertText: variable.key,
    detail: variable.type,
    documentation: variable.description || variable.pathDescription || `${variable.type} 类型变量`,
    sortText: `0_${variable.key}`,
  }));
};

// 获取函数建议
const getFunctionSuggestions = (): any[] => {
  return functions.map((func) => {
    // 生成详细的文档说明
    const documentation = generateFunctionDocumentation(func);

    // 生成函数签名作为detail
    const signature = generateFunctionSignature(func);

    return {
      label: func.value, // 使用英文函数名而不是中文label
      kind: monaco.languages.CompletionItemKind.Function,
      insertText: func.script,
      detail: signature, // 显示函数签名
      documentation: {
        value: documentation,
        isTrusted: true,
        supportHtml: true,
      },
      sortText: `1_${func.value}`,
    };
  });
};

// 生成函数签名
const generateFunctionSignature = (func: FunctionData): string => {
  const params =
    func.parameters
      ?.map((param) => {
        const optional = !param.required ? '?' : '';
        const isParams = param.isParams || false;

        // 如果是params参数，显示为...paramName: type
        if (isParams) {
          return `...${param.name}: ${param.type}[]`;
        }

        return `${param.name}${optional}: ${param.type}`;
      })
      .join(', ') || '';

  const returnType = func.returnType || 'any';
  return `${func.value}(${params}): ${returnType}`;
};

// 生成函数文档
const generateFunctionDocumentation = (func: FunctionData): string => {
  const parts = [];

  // 添加中文名称和描述
  parts.push(`**${func.label}** - ${func.remark}`);

  // 添加函数签名
  const signature = generateFunctionSignature(func);
  parts.push(`\`\`\`typescript\n${signature}\n\`\`\``);

  // 添加参数说明
  if (func.parameters && func.parameters.length > 0) {
    parts.push('**参数说明:**');
    func.parameters.forEach((param) => {
      const required = param.required ? '必需' : '可选';
      const defaultValue = param.defaultValue !== undefined ? ` (默认: ${param.defaultValue})` : '';
      parts.push(`- \`${param.name}\` (${param.type}, ${required}): ${param.description || ''}${defaultValue}`);
    });
  }

  // 添加返回值说明
  if (func.returnType) {
    parts.push(`**返回值:** ${func.outputType || func.returnType}`);
  }

  // 添加使用示例
  if (func.examples && func.examples.length > 0) {
    parts.push('**使用示例:**');
    func.examples.slice(0, 3).forEach((example, index) => {
      parts.push(`${index + 1}. **${example.title}**`);
      parts.push(`\`\`\`javascript\n${example.code}\n\`\`\``);
      if (example.result) {
        parts.push(`结果: \`${example.result}\``);
      }
      if (example.description) {
        parts.push(`说明: ${example.description}`);
      }
      parts.push('');
    });
  }

  return parts.join('\n\n');
};

// 获取内置对象和方法的建议
const getBuiltinSuggestions = (): any[] => {
  return [];
};

// 查找嵌套变量的子属性
const findNestedVariable = (variables: VariableData[], path: string): VariableData | null => {
  const pathParts = path.split('.');
  console.log('查找嵌套变量:', { path, pathParts });

  for (const variable of variables) {
    if (variable.key === pathParts[0]) {
      if (pathParts.length === 1) {
        console.log('找到目标变量:', variable.key, '子属性数量:', variable.children?.length || 0);
        return variable;
      }
      // 递归查找子属性
      if (variable.children && variable.children.length > 0) {
        const remainingPath = pathParts.slice(1).join('.');
        return findNestedVariable(variable.children, remainingPath);
      }
    }
  }
  return null;
};

// 检查是否应该提供自定义建议
const shouldProvideCustomSuggestions = (trimmedText: string, triggerCharacter?: string): boolean => {
  // 如果是通过点号触发的，只对Utils提供自定义建议
  if (triggerCharacter === '.') {
    // 如果是 Utils. 相关的访问，提供自定义建议
    if (trimmedText.includes('Utils.') || trimmedText.endsWith('Utils.')) {
      return true;
    }

    // 对于临时变量、_data等，完全依赖 TypeScript 类型定义，不提供自定义建议
    // 这样可以避免重复显示，让TypeScript原生智能提示处理所有属性访问
    return false;
  }

  // 如果不是点号触发，只在特定情况下提供自定义建议
  // 检查是否是在输入自定义变量名或内置对象
  const lastWord = trimmedText.split(/\s+/).pop() || '';
  const isTypingCustomVariable =
    currentVariables.some((v) => v.key.startsWith(lastWord)) ||
    lastWord.includes('_data') ||
    lastWord.includes('Utils');

  return isTypingCustomVariable;
};

// 注册TypeScript/JavaScript智能提示提供者
monaco.languages.registerCompletionItemProvider('typescript', {
  triggerCharacters: ['.', '_'],
  provideCompletionItems: async (model, position, context) => {
    const suggestions: any[] = [];
    const { lineNumber, column } = position;

    // 获取当前行的文本
    const textBeforePointer = model.getValueInRange({
      startLineNumber: lineNumber,
      startColumn: 0,
      endLineNumber: lineNumber,
      endColumn: column,
    });

    // 简化的输入分析 - 检查是否以特定模式结尾
    const trimmedText = textBeforePointer.trim();

    // 获取触发字符
    const triggerCharacter = context.triggerCharacter;

    console.log('智能提示触发:', {
      trimmedText,
      triggerCharacter,
      shouldProvideCustom: shouldProvideCustomSuggestions(trimmedText, triggerCharacter),
    });

    // 只在特定情况下提供自定义建议，避免与默认 TypeScript 提示重复
    if (shouldProvideCustomSuggestions(trimmedText, triggerCharacter)) {
      // 如果是在 Utils. 后面，提供函数建议
      if (trimmedText.endsWith('Utils.')) {
        const functionSuggestions = getFunctionSuggestions();
        suggestions.push(...functionSuggestions);
      }
      // 否则提供自定义变量建议（只在非点号触发时）
      else {
        // 添加临时变量建议（不需要 _data 前缀）
        const currentSuggestions = getFirstLevelSuggestions(currentVariables);
        suggestions.push(...currentSuggestions);

        // 添加内置对象建议
        const builtinSuggestions = getBuiltinSuggestions();
        suggestions.push(...builtinSuggestions);

        // 添加函数建议
        const functionSuggestions = getFunctionSuggestions();
        suggestions.push(...functionSuggestions);
      }

      // 去重处理，避免重复显示
      const uniqueSuggestions = deduplicateSuggestions(suggestions);

      return {
        suggestions: uniqueSuggestions,
        incomplete: uniqueSuggestions.length === 0, // 如果没有自定义建议，允许默认提示
      };
    }

    // 对于其他情况，返回空建议，让默认的 TypeScript 智能提示处理
    return {
      suggestions: [],
      incomplete: true, // 允许默认提示继续工作
    };
  },
});

// 同样为JavaScript语言注册
monaco.languages.registerCompletionItemProvider('javascript', {
  triggerCharacters: ['.', '_'],
  provideCompletionItems: async (model, position, context) => {
    const suggestions: any[] = [];
    const { lineNumber, column } = position;

    // 获取当前行的文本
    const textBeforePointer = model.getValueInRange({
      startLineNumber: lineNumber,
      startColumn: 0,
      endLineNumber: lineNumber,
      endColumn: column,
    });

    // 简化的输入分析 - 检查是否以特定模式结尾
    const trimmedText = textBeforePointer.trim();

    // 获取触发字符
    const triggerCharacter = context.triggerCharacter;

    // 只在特定情况下提供自定义建议
    if (shouldProvideCustomSuggestions(trimmedText, triggerCharacter)) {
      // 如果是在 Utils. 后面，提供函数建议
      if (trimmedText.endsWith('Utils.')) {
        const functionSuggestions = getFunctionSuggestions();
        suggestions.push(...functionSuggestions);
      }
      // 否则提供自定义变量建议（只在非点号触发时）
      else {
        // 添加临时变量建议（不需要 _data 前缀）
        const currentSuggestions = getFirstLevelSuggestions(currentVariables);
        suggestions.push(...currentSuggestions);

        // 添加内置对象建议
        const builtinSuggestions = getBuiltinSuggestions();
        suggestions.push(...builtinSuggestions);

        // 添加函数建议
        const functionSuggestions = getFunctionSuggestions();
        suggestions.push(...functionSuggestions);
      }

      // 去重处理，避免重复显示
      const uniqueSuggestions = deduplicateSuggestions(suggestions);

      return {
        suggestions: uniqueSuggestions,
        incomplete: uniqueSuggestions.length === 0, // 如果没有自定义建议，允许默认提示
      };
    }

    // 对于其他情况，返回空建议，让默认的 JavaScript 智能提示处理
    return {
      suggestions: [],
      incomplete: true, // 允许默认提示继续工作
    };
  },
});

// 提供Utils函数的参数提示
const provideUtilsSignatureHelp = (model: any, position: any) => {
  const { lineNumber, column } = position;

  // 获取当前行的文本
  const lineText = model.getLineContent(lineNumber);
  const textBeforePointer = lineText.substring(0, column - 1);

  // 查找Utils函数调用
  const utilsCallMatch = textBeforePointer.match(/Utils\.(\w+)\s*\(/);
  if (!utilsCallMatch) {
    return null;
  }

  const functionName = utilsCallMatch[1];
  const func = functions.find((f) => f.value === functionName);

  if (!func || !func.parameters || func.parameters.length === 0) {
    return null;
  }

  // 计算当前参数位置
  const functionCallStart = utilsCallMatch.index! + utilsCallMatch[0].length - 1; // 括号位置
  const currentParamText = textBeforePointer.substring(functionCallStart + 1);
  const commaCount = (currentParamText.match(/,/g) || []).length;
  const activeParameter = Math.min(commaCount, func.parameters.length - 1);

  // 生成参数信息
  const parameters = func.parameters.map((param) => {
    const isOptional = !param.required;
    const paramType = param.type === 'DateTime' ? 'Date' : param.type;
    const label = `${param.name}${isOptional ? '?' : ''}: ${paramType}`;

    return {
      label: label,
      documentation: {
        value: param.description || `参数 ${param.name}`,
        isTrusted: true,
      },
    };
  });

  // 生成函数签名
  const signature = {
    label: generateFunctionSignature(func),
    documentation: {
      value: `**${func.label}** - ${func.remark}`,
      isTrusted: true,
    },
    parameters: parameters,
    activeParameter: activeParameter,
  };

  return {
    value: {
      signatures: [signature],
      activeSignature: 0,
      activeParameter: activeParameter,
    },
    dispose: () => {},
  };
};

// 注册参数提示提供者 - TypeScript
monaco.languages.registerSignatureHelpProvider('typescript', {
  signatureHelpTriggerCharacters: ['(', ','],
  signatureHelpRetriggerCharacters: [','],
  provideSignatureHelp: (model, position) => {
    return provideUtilsSignatureHelp(model, position);
  },
});

// 注册参数提示提供者 - JavaScript
monaco.languages.registerSignatureHelpProvider('javascript', {
  signatureHelpTriggerCharacters: ['(', ','],
  signatureHelpRetriggerCharacters: [','],
  provideSignatureHelp: (model, position) => {
    return provideUtilsSignatureHelp(model, position);
  },
});
